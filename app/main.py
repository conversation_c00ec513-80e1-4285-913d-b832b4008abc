from toolbox.logger import get_logger
import logging
from toolbox.s3 import S3<PERSON><PERSON>per
import os
import signal
import sys
from types import FrameType
from typing import Optional
from config import config
from services.kafka_service import consumer, producer
from services.conversion_service import ConversionService
import json

kafka_logger = logging.getLogger("kafka")
kafka_logger.setLevel(logging.WARNING)

logger = get_logger(__name__)


def graceful_shutdown(signum: int, frame: Optional[FrameType]) -> None:
    logger.info(f"Received signal {signum}, initiating graceful shutdown...")
    try:
        consumer.close()
        producer.close()
        logger.info("Connections closed successfully")
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")
    sys.exit(0)


# Register signal handlers
signal.signal(signal.SIGTERM, graceful_shutdown)
signal.signal(signal.SIGINT, graceful_shutdown)


try:
    logger.info("Initializing worker")
    s3_helper = S3Helper(
        aws_access_key_id=config.S3_ACCESS_KEY_ID,
        aws_secret_access_key=config.S3_SECRET_ACCESS_KEY,
        endpoint_url=config.S3_ENDPOINT_URL,
        bucket_name=config.S3_BUCKET_NAME,
    )

    logger.info("Initialization succesful")
except Exception as e:
    logger.error(f"Initialization failed: {e}")
    sys.exit(1)

os.makedirs("resources", exist_ok=True)

for message in consumer:
    try:
        job_id = message.key
        logger.info(f"Received request: {message.value}", extra={"job_id": job_id})
        input_s3_path = message.value.get("s3_key")
        _, _, input_job_id, filename = input_s3_path.split("/")

        basename, extension = os.path.splitext(filename)
        input_local_path = f"resources/{filename}"

        s3_helper.download_file(input_s3_path, input_local_path)

        # Process the file
        format_type = message.value.get("parameters").get("format")

        # Load and process the input file
        with open(input_local_path, 'r') as f:
            input_data = json.load(f)

        # Convert based on format
        if format_type == "webvtt":
            converted_content = ConversionService.to_vtt(input_data)
            output_extension = ".vtt"
        elif format_type == "txt":
            converted_content = ConversionService.to_text(input_data)
            output_extension = ".txt"
        elif format_type == "srt":
            converted_content = ConversionService.to_srt(input_data)
            output_extension = ".srt"
        else:
            raise ValueError(f"Unsupported format: {format_type}")

        # Save converted content
        output_local_path = input_local_path.replace(extension, f"_result{output_extension}")
        with open(output_local_path, 'w') as f:
            f.write(converted_content)

        output_s3_path = input_s3_path.replace(extension, f"_result{output_extension}").replace(input_job_id, job_id)

        s3_helper.upload_file(output_local_path, object_key=output_s3_path)

        response = {
            "status": "ok",
            "message": "",
            "s3_key": output_s3_path,
        }

        producer.send(config.KAFKA_OUTPUT_TOPIC, response, message.key)
        producer.flush()

        consumer.commit()
        logger.info("Request processed succesfully", extra={"job_id": job_id})
    except Exception as e:
        logger.error(f"Error processing request: {e}", extra={"job_id": "system"})
        response = {
            "status": "error",
            "message": str(e),
            "s3_key": None
        }
        producer.send(config.KAFKA_OUTPUT_TOPIC, response, message.key)
