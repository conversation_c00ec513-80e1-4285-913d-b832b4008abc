from datetime import datetime, timezone


class ConversionService:
    """
    A service that handles conversion of data to different formats.
    """

    # Define a function to format seconds to WebVTT time format
    def format_time(seconds):
        return str(datetime.fromtimestamp(seconds, tz=timezone.utc).strftime("%H:%M:%S.%f")[:-3])

    # Create a WebVTT formatted string
    def to_vtt(data):
        webvtt_content = "WEBVTT\n\n"
        for i, segment in enumerate(data["segments"], start=1):
            start_time = ConversionService.format_time(segment["start"])
            end_time = ConversionService.format_time(segment["end"])
            text = segment["text"]
            webvtt_content += f"{start_time} --> {end_time}\n{text}\n\n"
        return webvtt_content

    def to_text(data):
        text = " ".join(segment["text"] for segment in data["segments"])
        return text
