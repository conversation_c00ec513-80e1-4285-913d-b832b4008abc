from datetime import datetime, timezone
from typing import Union, List, Dict, Any


class ConversionService:
    """
    A service that handles conversion of transcript data to different formats.

    Supports two input formats:
    1. Legacy format: {"segments": [{"text": "...", "start": 0.0, "end": 1.0}, ...]}
    2. Direct array format: [{"text": "...", "start": 0.0, "end": 1.0}, ...]
    """

    @staticmethod
    def _normalize_data(data: Union[Dict[str, Any], List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """
        Normalize input data to a consistent format (list of segments).

        Args:
            data: Either a dict with "segments" key or a direct list of segments

        Returns:
            List of segment dictionaries with "text", "start", and "end" keys

        Raises:
            ValueError: If the data format is not supported
        """
        if isinstance(data, dict):
            # Legacy format: {"segments": [...]}
            if "segments" in data:
                return data["segments"]
            else:
                raise ValueError("Dictionary input must contain 'segments' key")
        elif isinstance(data, list):
            # Direct array format: [{"text": "...", "start": 0.0, "end": 1.0}, ...]
            return data
        else:
            raise ValueError("Input data must be either a dictionary with 'segments' key or a list of segments")

    @staticmethod
    def _validate_segment(segment: Dict[str, Any]) -> None:
        """
        Validate that a segment has the required fields.

        Args:
            segment: Segment dictionary to validate

        Raises:
            ValueError: If required fields are missing
        """
        required_fields = ["text", "start", "end"]
        for field in required_fields:
            if field not in segment:
                raise ValueError(f"Segment missing required field: {field}")

    @staticmethod
    def format_time(seconds: float) -> str:
        """
        Format seconds to WebVTT time format (HH:MM:SS.mmm).

        Args:
            seconds: Time in seconds

        Returns:
            Formatted time string
        """
        return str(datetime.fromtimestamp(seconds, tz=timezone.utc).strftime("%H:%M:%S.%f")[:-3])

    @staticmethod
    def to_vtt(data: Union[Dict[str, Any], List[Dict[str, Any]]]) -> str:
        """
        Convert transcript data to WebVTT format.

        Args:
            data: Transcript data in either legacy or direct array format

        Returns:
            WebVTT formatted string

        Raises:
            ValueError: If data format is invalid or segments are malformed
        """
        segments = ConversionService._normalize_data(data)

        webvtt_content = "WEBVTT\n\n"
        for segment in segments:
            ConversionService._validate_segment(segment)

            start_time = ConversionService.format_time(segment["start"])
            end_time = ConversionService.format_time(segment["end"])
            text = segment["text"].strip()

            webvtt_content += f"{start_time} --> {end_time}\n{text}\n\n"

        return webvtt_content

    @staticmethod
    def to_text(data: Union[Dict[str, Any], List[Dict[str, Any]]]) -> str:
        """
        Convert transcript data to plain text format.

        Args:
            data: Transcript data in either legacy or direct array format

        Returns:
            Plain text string with all segments concatenated

        Raises:
            ValueError: If data format is invalid or segments are malformed
        """
        segments = ConversionService._normalize_data(data)

        text_parts = []
        for segment in segments:
            ConversionService._validate_segment(segment)
            text_parts.append(segment["text"].strip())

        return " ".join(text_parts)

    @staticmethod
    def to_srt(data: Union[Dict[str, Any], List[Dict[str, Any]]]) -> str:
        """
        Convert transcript data to SRT (SubRip) format.

        Args:
            data: Transcript data in either legacy or direct array format

        Returns:
            SRT formatted string

        Raises:
            ValueError: If data format is invalid or segments are malformed
        """
        segments = ConversionService._normalize_data(data)

        srt_content = ""
        for i, segment in enumerate(segments, start=1):
            ConversionService._validate_segment(segment)

            start_time = ConversionService._format_srt_time(segment["start"])
            end_time = ConversionService._format_srt_time(segment["end"])
            text = segment["text"].strip()

            srt_content += f"{i}\n{start_time} --> {end_time}\n{text}\n\n"

        return srt_content

    @staticmethod
    def _format_srt_time(seconds: float) -> str:
        """
        Format seconds to SRT time format (HH:MM:SS,mmm).

        Args:
            seconds: Time in seconds

        Returns:
            SRT formatted time string
        """
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        return f"{hours:02d}:{minutes:02d}:{secs:06.3f}".replace(".", ",")
