# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Development
.git/
.gitignore
*.md
README*
CHANGELOG*
LICENSE*
.pytest_cache/
.coverage
.mypy_cache/
.ruff_cache/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Jupyter
*.ipynb
.ipynb_checkpoints/

# Test files
test*/
tests/
*_test.py
test_*.py

# Documentation
docs/
*.md
CLAUDE.md

# OS
.DS_Store
Thumbs.db

# Temporary files
resources/
*.tmp
*.log