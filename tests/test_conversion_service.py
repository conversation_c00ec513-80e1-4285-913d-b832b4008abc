import unittest
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'app'))

from services.conversion_service import ConversionService


class TestConversionService(unittest.TestCase):
    """Test cases for the ConversionService class."""

    def setUp(self):
        """Set up test data in both legacy and direct array formats."""
        # Direct array format (new format)
        self.direct_array_data = [
            {"text": " J'vois pas pourquoi y'a rien qu'là sur Norman et y'a rien qu'là sur Cyprien, le Mongolien ! Moi je traîne aux Champs-Elysées, j'suis fait aigri ! Toi tu te fais recaler, hein <PERSON><PERSON><PERSON> ? Avec tes blagues de merde !", "start": 0.031, "end": 12.873},
            {"text": " Tu clashes avec quelqu'un, mais une vidéo en ligne Laisse-moi deviner, tu chercherais pas des amis J'hallucine, tu oses dire Cyprien le Mongolien ? Ok, très bien, rappelle-moi ton prénom Sé<PERSON>, tu dis avoir créé ta marque de vêtements Mais coller une feuille sur un t-shirt n'est pas suffisant", "start": 14.476, "end": 32.093},
            {"text": " Insulter les gens pour exister, je sais que c'est tentant Mais fais autre chose de ta vie, tu viens d'avoir 30 ans Ça y est ça me revient ta tête d'obèse moqueur On était ensemble au CP mais t'étais déjà majeur Quand on recherche ton blaze, donc google images On apprend tout sur toi rien que sur l'âme", "start": 32.093, "end": 49.205}
        ]
        
        # Legacy format (existing format)
        self.legacy_data = {
            "segments": self.direct_array_data
        }
        
        # Simple test data for easier verification
        self.simple_direct_data = [
            {"text": "Hello world", "start": 0.0, "end": 2.5},
            {"text": "This is a test", "start": 3.0, "end": 6.0}
        ]
        
        self.simple_legacy_data = {
            "segments": self.simple_direct_data
        }

    def test_normalize_data_legacy_format(self):
        """Test that legacy format data is correctly normalized."""
        result = ConversionService._normalize_data(self.legacy_data)
        self.assertEqual(result, self.direct_array_data)

    def test_normalize_data_direct_array_format(self):
        """Test that direct array format data is correctly normalized."""
        result = ConversionService._normalize_data(self.direct_array_data)
        self.assertEqual(result, self.direct_array_data)

    def test_normalize_data_invalid_dict(self):
        """Test that invalid dictionary format raises ValueError."""
        invalid_data = {"invalid_key": []}
        with self.assertRaises(ValueError) as context:
            ConversionService._normalize_data(invalid_data)
        self.assertIn("Dictionary input must contain 'segments' key", str(context.exception))

    def test_normalize_data_invalid_type(self):
        """Test that invalid data type raises ValueError."""
        invalid_data = "invalid string"
        with self.assertRaises(ValueError) as context:
            ConversionService._normalize_data(invalid_data)
        self.assertIn("Input data must be either a dictionary", str(context.exception))

    def test_validate_segment_valid(self):
        """Test that valid segments pass validation."""
        valid_segment = {"text": "Hello", "start": 0.0, "end": 1.0}
        # Should not raise any exception
        ConversionService._validate_segment(valid_segment)

    def test_validate_segment_missing_text(self):
        """Test that segment missing text field raises ValueError."""
        invalid_segment = {"start": 0.0, "end": 1.0}
        with self.assertRaises(ValueError) as context:
            ConversionService._validate_segment(invalid_segment)
        self.assertIn("Segment missing required field: text", str(context.exception))

    def test_validate_segment_missing_start(self):
        """Test that segment missing start field raises ValueError."""
        invalid_segment = {"text": "Hello", "end": 1.0}
        with self.assertRaises(ValueError) as context:
            ConversionService._validate_segment(invalid_segment)
        self.assertIn("Segment missing required field: start", str(context.exception))

    def test_validate_segment_missing_end(self):
        """Test that segment missing end field raises ValueError."""
        invalid_segment = {"text": "Hello", "start": 0.0}
        with self.assertRaises(ValueError) as context:
            ConversionService._validate_segment(invalid_segment)
        self.assertIn("Segment missing required field: end", str(context.exception))

    def test_format_time(self):
        """Test time formatting for WebVTT."""
        # Test various time values
        self.assertEqual(ConversionService.format_time(0.0), "00:00:00.000")
        self.assertEqual(ConversionService.format_time(1.5), "00:00:01.500")
        self.assertEqual(ConversionService.format_time(65.123), "00:01:05.123")
        self.assertEqual(ConversionService.format_time(3661.456), "01:01:01.456")

    def test_format_srt_time(self):
        """Test time formatting for SRT."""
        # Test various time values
        self.assertEqual(ConversionService._format_srt_time(0.0), "00:00:00,000")
        self.assertEqual(ConversionService._format_srt_time(1.5), "00:00:01,500")
        self.assertEqual(ConversionService._format_srt_time(65.123), "00:01:05,123")
        self.assertEqual(ConversionService._format_srt_time(3661.456), "01:01:01,456")

    def test_to_vtt_legacy_format(self):
        """Test WebVTT conversion with legacy format."""
        result = ConversionService.to_vtt(self.simple_legacy_data)
        expected = "WEBVTT\n\n00:00:00.000 --> 00:00:02.500\nHello world\n\n00:00:03.000 --> 00:00:06.000\nThis is a test\n\n"
        self.assertEqual(result, expected)

    def test_to_vtt_direct_array_format(self):
        """Test WebVTT conversion with direct array format."""
        result = ConversionService.to_vtt(self.simple_direct_data)
        expected = "WEBVTT\n\n00:00:00.000 --> 00:00:02.500\nHello world\n\n00:00:03.000 --> 00:00:06.000\nThis is a test\n\n"
        self.assertEqual(result, expected)

    def test_to_text_legacy_format(self):
        """Test text conversion with legacy format."""
        result = ConversionService.to_text(self.simple_legacy_data)
        expected = "Hello world This is a test"
        self.assertEqual(result, expected)

    def test_to_text_direct_array_format(self):
        """Test text conversion with direct array format."""
        result = ConversionService.to_text(self.simple_direct_data)
        expected = "Hello world This is a test"
        self.assertEqual(result, expected)

    def test_to_srt_legacy_format(self):
        """Test SRT conversion with legacy format."""
        result = ConversionService.to_srt(self.simple_legacy_data)
        expected = "1\n00:00:00,000 --> 00:00:02,500\nHello world\n\n2\n00:00:03,000 --> 00:00:06,000\nThis is a test\n\n"
        self.assertEqual(result, expected)

    def test_to_srt_direct_array_format(self):
        """Test SRT conversion with direct array format."""
        result = ConversionService.to_srt(self.simple_direct_data)
        expected = "1\n00:00:00,000 --> 00:00:02,500\nHello world\n\n2\n00:00:03,000 --> 00:00:06,000\nThis is a test\n\n"
        self.assertEqual(result, expected)

    def test_real_data_conversion(self):
        """Test conversion with the real French transcript data."""
        # Test that both formats produce the same output
        vtt_legacy = ConversionService.to_vtt(self.legacy_data)
        vtt_direct = ConversionService.to_vtt(self.direct_array_data)
        self.assertEqual(vtt_legacy, vtt_direct)
        
        text_legacy = ConversionService.to_text(self.legacy_data)
        text_direct = ConversionService.to_text(self.direct_array_data)
        self.assertEqual(text_legacy, text_direct)
        
        srt_legacy = ConversionService.to_srt(self.legacy_data)
        srt_direct = ConversionService.to_srt(self.direct_array_data)
        self.assertEqual(srt_legacy, srt_direct)

    def test_text_stripping(self):
        """Test that leading/trailing whitespace is properly stripped."""
        data_with_whitespace = [
            {"text": "  Hello world  ", "start": 0.0, "end": 2.5},
            {"text": "\tThis is a test\n", "start": 3.0, "end": 6.0}
        ]
        
        text_result = ConversionService.to_text(data_with_whitespace)
        self.assertEqual(text_result, "Hello world This is a test")
        
        vtt_result = ConversionService.to_vtt(data_with_whitespace)
        self.assertIn("Hello world\n\n", vtt_result)
        self.assertIn("This is a test\n\n", vtt_result)


if __name__ == '__main__':
    unittest.main()
